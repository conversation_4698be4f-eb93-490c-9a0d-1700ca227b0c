# Fincloud UI MCP Server

A Model Context Protocol (MCP) server that provides intelligent code completion, documentation, and analysis for the Fincloud UI component library.

## Features

### 🚀 Code Completion

- **Angular Template Completion**: Intelligent suggestions for Fincloud components in HTML templates
- **TypeScript Completion**: Import path suggestions and component API completions
- **Context-Aware Suggestions**: Smart completions based on current file context and component relationships

### 📚 Documentation Generation

- **Component Documentation**: Automatic generation of comprehensive component docs
- **API Reference**: Detailed input/output and method documentation
- **Usage Examples**: Code examples for basic, advanced, and form integration scenarios
- **Accessibility Guidelines**: Built-in accessibility recommendations

### 🔍 Code Analysis

- **Performance Analysis**: OnPush change detection, bundle size optimization, memory leak detection
- **Best Practices**: Angular coding standards, naming conventions, error handling
- **Accessibility Audits**: ARIA labels, keyboard navigation, semantic HTML validation
- **Fincloud-Specific Rules**: Component usage patterns and migration suggestions

## Installation

```bash
npm install
npm run build
```

## Configuration

Create a configuration file in your project root:

### `fincloud-ui-mcp.config.json`

```json
{
  "libraryPath": "/path/to/fincloud-ui-library",
  "cacheDir": ".mcp-cache",
  "watchFiles": true,
  "analysisRules": {
    "enforceOnPush": true,
    "checkAccessibility": true,
    "validateImports": true,
    "performanceChecks": true
  },
  "completionSettings": {
    "includePrivateMembers": false,
    "fuzzyMatching": true,
    "maxSuggestions": 50,
    "prioritizeRecentlyUsed": true
  }
}
```

### Alternative: `package.json`

```json
{
  "fincloudUiMcp": {
    "libraryPath": "./libs",
    "analysisRules": {
      "enforceOnPush": true
    }
  }
}
```

## Usage

### Starting the Server

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start

# Build index manually
npm run index
```

### Integration into Other Projects

#### Quick Setup (Automated)

```bash
# In your project directory
node /path/to/fincloud-ui-mcp-server/setup-mcp.js
```

#### Manual Setup

Create `.mcp.json` in your project root:

```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "node",
      "args": ["../fincloud-ui-mcp-server/dist/index.js"],
      "cwd": "../fincloud-ui-mcp-server",
      "env": {
        "FINCLOUD_LIBRARY_PATH": "../lib-ui-2"
      }
    }
  }
}
```

#### Global Installation

```bash
npm install -g .
```

Then use in any project:

```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "fincloud-ui-mcp",
      "env": {
        "FINCLOUD_LIBRARY_PATH": "/path/to/lib-ui-2"
      }
    }
  }
}
```

See [INTEGRATION.md](./INTEGRATION.md) for detailed setup instructions and examples.

### IDE Integration

#### VS Code with Claude Code

The server works seamlessly with Claude Code extension. After adding `.mcp.json` to your project, restart VS Code and the server will be available.

#### Other IDEs

Refer to your IDE's MCP integration documentation for specific setup instructions.

## MCP Tools

### `get_component_completions`

Get intelligent code completion suggestions for Angular templates and TypeScript files.

```json
{
  "context": "<fin-button",
  "position": { "line": 5, "character": 12 },
  "filePath": "/path/to/component.html"
}
```

### `get_component_documentation`

Generate comprehensive documentation for any Fincloud component.

```json
{
  "componentName": "FinButtonComponent",
  "includeExamples": true
}
```

### `analyze_code`

Perform code analysis for performance, accessibility, and best practices.

```json
{
  "filePath": "/path/to/component.ts",
  "analysisType": "all"
}
```

### `search_components`

Search for components by name, functionality, or category.

```json
{
  "query": "button",
  "category": "action",
  "includeUtils": false
}
```

### `get_import_suggestions`

Get optimal import path suggestions for components and utilities.

```json
{
  "symbol": "FinButtonComponent",
  "currentFilePath": "/path/to/current/file.ts"
}
```

### `get_related_components`

Find components related to a given component.

```json
{
  "componentName": "FinButtonComponent",
  "relationType": "commonly-used-with"
}
```

## Architecture

### Core Components

```
src/
├── core/              # MCP server implementation
├── indexers/          # Component discovery and cataloging
├── parsers/           # TypeScript/Angular code parsing
├── completions/       # Code completion providers
├── documentation/     # Documentation generation
├── analysis/          # Code analysis and validation
└── utils/             # Configuration and helpers
```

### Data Flow

1. **Indexing Phase**: Scans `libs/ui` and `libs/utils` directories
2. **Parsing Phase**: Extracts component metadata, APIs, and relationships
3. **Caching Phase**: Stores processed data for fast retrieval
4. **Query Phase**: Responds to IDE requests with contextual suggestions

## Performance

### Caching Strategy

- **Component Index Cache**: Persistent storage of parsed component metadata
- **Incremental Updates**: File watcher for efficient re-indexing
- **Smart Cache Invalidation**: 24-hour TTL with content-based validation

### Memory Optimization

- **Lazy Loading**: Components loaded on-demand
- **Efficient Search**: Fuse.js for fast fuzzy searching
- **Memory Management**: Automatic cleanup of unused data

## Examples

### Component Completion

```typescript
// Typing '<fin-' in template triggers:
<fin-button size="medium" appearance="primary">
  Click me
</fin-button>
```

### Import Suggestions

```typescript
// Typing 'FinButton' suggests:
import { FinButtonComponent } from "@fincloud/ui/button";
```

### Documentation Generation

```markdown
# FinButtonComponent

A clickable element used to perform actions or submit forms.

## API Reference

### Inputs

| Name       | Type                | Required | Description          |
| ---------- | ------------------- | -------- | -------------------- |
| size       | FinSize             | false    | Size of the button   |
| appearance | FinButtonAppearance | false    | Visual style variant |

### Outputs

| Name    | Type               | Description                    |
| ------- | ------------------ | ------------------------------ |
| clicked | EventEmitter<void> | Emitted when button is clicked |
```

## Development

### Building

```bash
npm run build
```

### Testing

```bash
npm test
```

### Linting

```bash
npm run lint
npm run lint:fix
```

### Development Mode

```bash
npm run dev
```

## Troubleshooting

### Common Issues

#### Index Not Building

- Verify `libraryPath` points to correct directory
- Ensure `libs/ui` and `libs/utils` directories exist
- Check file permissions for cache directory

#### Completions Not Working

- Restart the MCP server
- Clear cache directory and rebuild index
- Verify TypeScript configuration is correct

#### Performance Issues

- Disable file watching in production: `"watchFiles": false`
- Reduce completion suggestions: `"maxSuggestions": 20`
- Check cache directory disk space

### Debug Mode

Set environment variable for verbose logging:

```bash
DEBUG=fincloud-ui-mcp npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and feature requests, please use the GitHub issue tracker.

---

**Powered by**: Model Context Protocol (MCP) • TypeScript • Angular • Fuse.js
