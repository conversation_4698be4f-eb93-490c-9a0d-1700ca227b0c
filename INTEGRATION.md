# Fincloud UI MCP Server Integration Guide

This guide explains how to integrate the Fincloud UI MCP Server into your development projects.

## Quick Setup

### Automated Setup (Recommended)

```bash
# In your project directory
node /path/to/fincloud-ui-mcp-server/setup-mcp.js [setup-type]
```

Setup types:
- `local` - Local development (default)
- `global` - Global npm installation
- `absolute` - Absolute paths

### Manual Setup

1. Create `.mcp.json` in your project root
2. Configure the server path and library path
3. Restart your IDE/editor

## Configuration Examples

### Local Development

**Project Structure:**
```
/your-workspace/
├── fincloud-ui-mcp-server/
├── lib-ui-2/
└── my-project/
    └── .mcp.json
```

**`.mcp.json`:**
```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "node",
      "args": ["../fincloud-ui-mcp-server/dist/index.js"],
      "cwd": "../fincloud-ui-mcp-server",
      "env": {
        "FINCLOUD_LIBRARY_PATH": "../lib-ui-2"
      }
    }
  }
}
```

### Global Installation

```bash
# Install globally
cd /path/to/fincloud-ui-mcp-server
npm install -g .
```

**`.mcp.json`:**
```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "fincloud-ui-mcp",
      "env": {
        "FINCLOUD_LIBRARY_PATH": "/absolute/path/to/lib-ui-2"
      }
    }
  }
}
```

### Team/Shared Setup

**`.mcp.json`:**
```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "node",
      "args": ["${FINCLOUD_MCP_SERVER_PATH}/dist/index.js"],
      "cwd": "${FINCLOUD_MCP_SERVER_PATH}",
      "env": {
        "FINCLOUD_LIBRARY_PATH": "${FINCLOUD_UI_ROOT}"
      }
    }
  }
}
```

**Team `.env` file:**
```bash
FINCLOUD_MCP_SERVER_PATH=/shared/tools/fincloud-ui-mcp-server
FINCLOUD_UI_ROOT=/shared/libraries/lib-ui-2
```

## Environment Variables

The server supports these environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `FINCLOUD_LIBRARY_PATH` | Path to Fincloud UI library | Current directory |
| `FINCLOUD_CACHE_DIR` | Cache directory | `.mcp-cache` |
| `FINCLOUD_WATCH_FILES` | Enable file watching | `true` |

## Configuration Files

### Server Configuration

The server looks for configuration in this order:

1. `fincloud-ui-mcp.config.json` (recommended)
2. `.fincloud-ui-mcp.json`
3. `fincloudUiMcp` section in `package.json`

**Example `fincloud-ui-mcp.config.json`:**
```json
{
  "libraryPath": "../lib-ui-2",
  "cacheDir": ".mcp-cache",
  "watchFiles": true,
  "analysisRules": {
    "enforceOnPush": true,
    "checkAccessibility": true,
    "validateImports": true,
    "performanceChecks": true
  },
  "completionSettings": {
    "includePrivateMembers": false,
    "fuzzyMatching": true,
    "maxSuggestions": 50,
    "prioritizeRecentlyUsed": true
  }
}
```

## Troubleshooting

### Common Issues

#### "Library path does not exist"
- Verify the `libraryPath` points to a directory containing `libs/ui`
- Check that paths are relative to the server's working directory
- Use absolute paths if relative paths are problematic

#### "Server startup timeout"
- Ensure the server can access the Fincloud UI library
- Check file permissions
- Verify Node.js version (requires 18+)

#### "No completions appearing"
- Restart your IDE/editor after configuration changes
- Check the MCP server logs for errors
- Verify the server is listed in your IDE's MCP configuration

### Debug Mode

Enable debug logging:
```bash
DEBUG=fincloud-ui-mcp npm start
```

Or set environment variable in `.mcp.json`:
```json
{
  "mcpServers": {
    "fincloud-ui": {
      "command": "node",
      "args": ["../fincloud-ui-mcp-server/dist/index.js"],
      "cwd": "../fincloud-ui-mcp-server",
      "env": {
        "DEBUG": "fincloud-ui-mcp",
        "FINCLOUD_LIBRARY_PATH": "../lib-ui-2"
      }
    }
  }
}
```

## IDE-Specific Setup

### VS Code with Claude Code Extension

1. Install the Claude Code extension
2. Add `.mcp.json` to your project root
3. Restart VS Code
4. The server should appear in Claude Code's MCP servers list

### Other IDEs

Refer to your IDE's MCP integration documentation for specific setup instructions.

## Verification

Test the integration:

1. Open a TypeScript/Angular file
2. Start typing a Fincloud component: `<fin-button`
3. You should see intelligent completions
4. Ask your AI assistant about Fincloud components

## Best Practices

1. **Use absolute paths** for production/team setups
2. **Version control** your `.mcp.json` file
3. **Document** the required environment variables for your team
4. **Test** the setup on a clean environment
5. **Keep** the server updated with `npm update`

## Support

For issues and questions:
- Check the server logs for error messages
- Verify your configuration against the examples
- Ensure all paths exist and are accessible
- Test with a minimal configuration first
