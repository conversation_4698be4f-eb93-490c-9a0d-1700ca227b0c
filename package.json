{"name": "@fincloud/ui-mcp-server", "version": "0.0.3", "description": "Model Context Protocol server for Fincloud UI component library", "main": "dist/index.js", "bin": {"fincloud-ui-mcp": "dist/index.js"}, "files": ["dist/**/*", "README.md", "LICENSE"], "publishConfig": {"@fincloud:registry": "https://nexus-v2.neo.loan/repository/npm/", "access": "restricted"}, "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "index": "tsx src/scripts/build-index.ts"}, "keywords": ["mcp", "model-context-protocol", "fincloud", "ui-library", "angular", "typescript", "code-completion"], "author": "Fincloud UI Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "@typescript-eslint/parser": "^6.21.0", "typescript": "^5.8.3", "glob": "^10.3.10", "fast-glob": "^3.3.2", "chokidar": "^3.5.3", "lodash-es": "^4.17.21", "fuse.js": "^7.0.0"}, "devDependencies": {"@types/node": "^20.11.16", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.12", "tsx": "^4.7.0", "ts-jest": "^29.1.2"}, "engines": {"node": ">=18.0.0"}}