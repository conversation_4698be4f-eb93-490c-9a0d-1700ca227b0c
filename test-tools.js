#!/usr/bin/env node

/**
 * Test individual tools to ensure they work with the new MCP SDK
 */

import { spawn } from "child_process";

async function testTools() {
  console.log("🔧 Testing individual MCP tools...");
  
  return new Promise((resolve, reject) => {
    const serverProcess = spawn("node", ["dist/index.js"], {
      stdio: ["pipe", "pipe", "pipe"],
    });

    let initialized = false;
    let requestId = 1;
    const pendingRequests = new Map();

    const sendRequest = (method, params = {}) => {
      const id = requestId++;
      const request = {
        jsonrpc: "2.0",
        id,
        method,
        params
      };

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          pendingRequests.delete(id);
          reject(new Error(`Request timeout for ${method}`));
        }, 5000);

        pendingRequests.set(id, { resolve, reject, timeout });
        serverProcess.stdin.write(JSON.stringify(request) + "\n");
      });
    };

    serverProcess.stdout.on("data", (data) => {
      try {
        const response = JSON.parse(data.toString());
        
        if (response.id && pendingRequests.has(response.id)) {
          const { resolve, reject, timeout } = pendingRequests.get(response.id);
          clearTimeout(timeout);
          pendingRequests.delete(response.id);

          if (response.error) {
            reject(new Error(response.error.message || "Unknown error"));
          } else {
            resolve(response.result);
          }
        }
      } catch (error) {
        // Ignore non-JSON output
      }
    });

    serverProcess.stderr.on("data", (data) => {
      const message = data.toString();
      if (message.includes("running on stdio") && !initialized) {
        initialized = true;
        runToolTests();
      }
    });

    async function runToolTests() {
      try {
        // Initialize
        await sendRequest("initialize", {
          protocolVersion: "2024-11-05",
          capabilities: { resources: {}, tools: {} },
          clientInfo: { name: "test-client", version: "1.0.0" }
        });
        console.log("✅ Server initialized");

        // Test list tools
        const tools = await sendRequest("tools/list");
        console.log(`✅ Found ${tools.tools.length} tools`);

        // Test list resources  
        const resources = await sendRequest("resources/list");
        console.log(`✅ Found ${resources.resources.length} resources`);

        // Test a simple tool call (search_components)
        try {
          const searchResult = await sendRequest("tools/call", {
            name: "search_components",
            arguments: { query: "test" }
          });
          console.log("✅ search_components tool works");
        } catch (error) {
          console.log("⚠️  search_components tool error (expected due to missing index):", error.message);
        }

        // Test resource reading
        if (resources.resources.length > 0) {
          try {
            const resourceContent = await sendRequest("resources/read", {
              uri: resources.resources[0].uri
            });
            console.log("✅ Resource reading works");
          } catch (error) {
            console.log("⚠️  Resource reading error (expected due to missing index):", error.message);
          }
        }

        serverProcess.kill();
        resolve();

      } catch (error) {
        serverProcess.kill();
        reject(error);
      }
    }

    setTimeout(() => {
      if (!initialized) {
        serverProcess.kill();
        reject(new Error("Server initialization timeout"));
      }
    }, 15000);
  });
}

async function main() {
  try {
    await testTools();
    console.log("\n🎉 Tool testing completed successfully!");
  } catch (error) {
    console.error("\n💥 Tool testing failed:", error.message);
    process.exit(1);
  }
}

main();
