import { readFile } from 'fs/promises';
import { existsSync } from 'fs';
import * as ts from 'typescript';

import { AnalysisResult, CodeFix, Range } from '../types/schema.js';
import { FincloudUIIndexer } from '../indexers/component-indexer.js';
import { ConfigManager } from '../utils/config-manager.js';

export interface AnalysisOptions {
  filePath?: string;
  code?: string;
  analysisType: 'all' | 'performance' | 'accessibility' | 'best-practices';
}

export interface PerformanceReport {
  changeDetectionOptimization: AnalysisResult[];
  bundleSizeImpact: BundleSizeImpact;
  runtimeComplexity: RuntimeComplexity;
  memoryUsage: MemoryUsage;
  recommendations: string[];
}

export interface BundleSizeImpact {
  estimatedSize: number;
  heavyDependencies: string[];
  optimizationSuggestions: string[];
}

export interface RuntimeComplexity {
  cyclomaticComplexity: number;
  nestedLoops: number;
  recursiveCallsDetected: boolean;
}

export interface MemoryUsage {
  potentialMemoryLeaks: string[];
  largeObjectAllocations: string[];
  unsubscribedObservables: string[];
}

export class CodeAnalyzer {
  private indexer: FincloudUIIndexer;
  private configManager: ConfigManager;

  constructor(indexer: FincloudUIIndexer) {
    this.indexer = indexer;
    this.configManager = indexer['configManager']; // Access through indexer
  }

  async analyzeCode(options: AnalysisOptions): Promise<AnalysisResult[]> {
    const { filePath, code, analysisType } = options;
    
    let sourceCode: string;
    if (code) {
      sourceCode = code;
    } else if (filePath && existsSync(filePath)) {
      sourceCode = await readFile(filePath, 'utf-8');
    } else {
      throw new Error('Either filePath or code must be provided');
    }

    const results: AnalysisResult[] = [];

    switch (analysisType) {
      case 'all':
        results.push(...await this.analyzePerformance(sourceCode, filePath));
        results.push(...await this.analyzeAccessibility(sourceCode, filePath));
        results.push(...await this.analyzeBestPractices(sourceCode, filePath));
        break;
      case 'performance':
        results.push(...await this.analyzePerformance(sourceCode, filePath));
        break;
      case 'accessibility':
        results.push(...await this.analyzeAccessibility(sourceCode, filePath));
        break;
      case 'best-practices':
        results.push(...await this.analyzeBestPractices(sourceCode, filePath));
        break;
    }

    return results;
  }

  private async analyzePerformance(code: string, filePath?: string): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = [];
    const sourceFile = ts.createSourceFile(
      filePath || 'temp.ts',
      code,
      ts.ScriptTarget.Latest,
      true
    );

    // Check for OnPush change detection
    if (this.isAngularComponent(sourceFile)) {
      const hasOnPush = this.hasOnPushChangeDetection(sourceFile);
      if (!hasOnPush) {
        results.push({
          type: 'warning',
          message: 'Consider using OnPush change detection strategy for better performance',
          suggestion: 'Add ChangeDetectionStrategy.OnPush to your component decorator',
          fix: this.generateOnPushFix(sourceFile),
        });
      }
    }

    // Check for unnecessary re-renders
    const trackByFunctions = this.findTrackByFunctions(code);
    if (this.hasNgForWithoutTrackBy(code) && trackByFunctions.length === 0) {
      results.push({
        type: 'warning',
        message: 'ngFor detected without trackBy function',
        suggestion: 'Add trackBy function to improve list rendering performance',
        fix: this.generateTrackByFix(),
      });
    }

    // Check for large bundle imports
    const heavyImports = this.detectHeavyImports(sourceFile);
    for (const heavyImport of heavyImports) {
      results.push({
        type: 'info',
        message: `Heavy library import detected: ${heavyImport}`,
        suggestion: 'Consider tree-shaking or using lighter alternatives',
      });
    }

    // Check for potential memory leaks
    const memoryLeaks = this.detectPotentialMemoryLeaks(sourceFile);
    results.push(...memoryLeaks);

    return results;
  }

  private async analyzeAccessibility(code: string, filePath?: string): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = [];

    // Check for missing ARIA labels
    const missingAriaLabels = this.findMissingAriaLabels(code);
    for (const missing of missingAriaLabels) {
      results.push({
        type: 'warning',
        message: `Missing ARIA label for ${missing.element}`,
        suggestion: `Add aria-label or aria-labelledby to improve accessibility`,
        range: missing.range,
      });
    }

    // Check for keyboard navigation support
    if (this.hasClickHandlersWithoutKeyboard(code)) {
      results.push({
        type: 'warning',
        message: 'Interactive elements should support keyboard navigation',
        suggestion: 'Add keydown handlers for Enter and Space keys',
      });
    }

    // Check for sufficient color contrast (basic check)
    const colorContrastIssues = this.checkColorContrast(code);
    results.push(...colorContrastIssues);

    // Check for semantic HTML usage
    const semanticIssues = this.checkSemanticHTML(code);
    results.push(...semanticIssues);

    return results;
  }

  private async analyzeBestPractices(code: string, filePath?: string): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = [];
    const sourceFile = ts.createSourceFile(
      filePath || 'temp.ts',
      code,
      ts.ScriptTarget.Latest,
      true
    );

    // Check for proper input/output naming
    const namingIssues = this.validateInputOutputNaming(sourceFile);
    results.push(...namingIssues);

    // Check for unused imports
    const unusedImports = this.findUnusedImports(sourceFile);
    for (const unused of unusedImports) {
      results.push({
        type: 'info',
        message: `Unused import: ${unused.name}`,
        suggestion: 'Remove unused import to reduce bundle size',
        fix: this.generateRemoveImportFix(unused),
      });
    }

    // Check for proper error handling
    if (this.lacksErrorHandling(sourceFile)) {
      results.push({
        type: 'warning',
        message: 'Missing error handling in HTTP requests or observables',
        suggestion: 'Add proper error handling with catchError operator',
      });
    }

    // Check for magic numbers/strings
    const magicValues = this.findMagicValues(sourceFile);
    for (const magic of magicValues) {
      results.push({
        type: 'info',
        message: `Magic ${magic.type} detected: ${magic.value}`,
        suggestion: 'Consider extracting to a named constant',
        range: magic.range,
      });
    }

    // Check for Fincloud UI component usage best practices
    const fincloudIssues = this.analyzeFincloudUsage(code);
    results.push(...fincloudIssues);

    return results;
  }

  async generatePerformanceReport(componentName: string): Promise<PerformanceReport> {
    const metadata = this.indexer.getComponentMetadata(componentName);
    if (!metadata) {
      throw new Error(`Component ${componentName} not found`);
    }

    const code = await readFile(metadata.filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(
      metadata.filePath,
      code,
      ts.ScriptTarget.Latest,
      true
    );

    const changeDetectionOptimization = await this.analyzePerformance(code, metadata.filePath);
    const bundleSizeImpact = this.calculateBundleSizeImpact(sourceFile, metadata);
    const runtimeComplexity = this.analyzeRuntimeComplexity(sourceFile);
    const memoryUsage = this.analyzeMemoryUsage(sourceFile);
    const recommendations = this.generatePerformanceRecommendations(
      changeDetectionOptimization,
      bundleSizeImpact,
      runtimeComplexity,
      memoryUsage
    );

    return {
      changeDetectionOptimization,
      bundleSizeImpact,
      runtimeComplexity,
      memoryUsage,
      recommendations,
    };
  }

  // Helper methods for performance analysis

  private isAngularComponent(sourceFile: ts.SourceFile): boolean {
    let isComponent = false;
    
    ts.forEachChild(sourceFile, (node) => {
      if (ts.isClassDeclaration(node) && node.modifiers) {
        const hasComponentDecorator = node.modifiers.some((modifier: ts.ModifierLike) =>
          ts.isDecorator(modifier) &&
          ts.isCallExpression(modifier.expression) &&
          ts.isIdentifier(modifier.expression.expression) &&
          modifier.expression.expression.text === 'Component'
        );
        if (hasComponentDecorator) {
          isComponent = true;
        }
      }
    });
    
    return isComponent;
  }

  private hasOnPushChangeDetection(sourceFile: ts.SourceFile): boolean {
    let hasOnPush = false;
    
    ts.forEachChild(sourceFile, (node) => {
      if (ts.isClassDeclaration(node) && node.modifiers) {
        node.modifiers.forEach((modifier: ts.ModifierLike) => {
          if (!ts.isDecorator(modifier)) return;
          const decorator = modifier as ts.Decorator;
          if (ts.isCallExpression(decorator.expression) && 
              decorator.expression.arguments.length > 0) {
            const arg = decorator.expression.arguments[0];
            if (ts.isObjectLiteralExpression(arg)) {
              arg.properties.forEach(prop => {
                if (ts.isPropertyAssignment(prop) &&
                    ts.isIdentifier(prop.name) &&
                    prop.name.text === 'changeDetection') {
                  hasOnPush = prop.initializer.getText().includes('OnPush');
                }
              });
            }
          }
        });
      }
    });
    
    return hasOnPush;
  }

  private findTrackByFunctions(code: string): string[] {
    const trackByRegex = /trackBy\s*:\s*(\w+)/g;
    const matches = [];
    let match;
    
    while ((match = trackByRegex.exec(code)) !== null) {
      matches.push(match[1]);
    }
    
    return matches;
  }

  private hasNgForWithoutTrackBy(code: string): boolean {
    const ngForRegex = /\*ngFor="[^"]*"/g;
    const matches = code.match(ngForRegex);
    
    if (!matches) return false;
    
    return matches.some(match => !match.includes('trackBy'));
  }

  private detectHeavyImports(sourceFile: ts.SourceFile): string[] {
    const heavyLibraries = [
      'lodash', 
      'moment', 
      '@angular/material',
      'primeng',
      'chart.js'
    ];
    
    const heavyImports: string[] = [];
    
    ts.forEachChild(sourceFile, (node) => {
      if (ts.isImportDeclaration(node) && 
          ts.isStringLiteral(node.moduleSpecifier)) {
        const moduleName = node.moduleSpecifier.text;
        
        if (heavyLibraries.some(heavy => moduleName.includes(heavy))) {
          heavyImports.push(moduleName);
        }
      }
    });
    
    return heavyImports;
  }

  private detectPotentialMemoryLeaks(sourceFile: ts.SourceFile): AnalysisResult[] {
    const results: AnalysisResult[] = [];
    const hasUnsubscribe = this.hasUnsubscribeLogic(sourceFile);
    const hasSubscriptions = this.hasObservableSubscriptions(sourceFile);
    
    if (hasSubscriptions && !hasUnsubscribe) {
      results.push({
        type: 'warning',
        message: 'Observable subscriptions detected without unsubscribe logic',
        suggestion: 'Implement OnDestroy and unsubscribe from observables to prevent memory leaks',
        fix: this.generateUnsubscribeFix(),
      });
    }
    
    return results;
  }

  private hasUnsubscribeLogic(sourceFile: ts.SourceFile): boolean {
    const sourceText = sourceFile.getText();
    return sourceText.includes('unsubscribe') || 
           sourceText.includes('takeUntil') ||
           sourceText.includes('async');
  }

  private hasObservableSubscriptions(sourceFile: ts.SourceFile): boolean {
    return sourceFile.getText().includes('.subscribe(');
  }

  // Helper methods for accessibility analysis

  private findMissingAriaLabels(code: string): Array<{ element: string; range?: Range }> {
    const interactiveElements = ['button', 'fin-button', 'a', 'input'];
    const missing: Array<{ element: string; range?: Range }> = [];
    
    for (const element of interactiveElements) {
      const regex = new RegExp(`<${element}(?![^>]*aria-label)(?![^>]*aria-labelledby)[^>]*>`, 'g');
      let match;
      
      while ((match = regex.exec(code)) !== null) {
        missing.push({
          element,
          // range calculation would need more sophisticated parsing
        });
      }
    }
    
    return missing;
  }

  private hasClickHandlersWithoutKeyboard(code: string): boolean {
    const clickRegex = /\(click\)="[^"]*"/g;
    const keydownRegex = /\(keydown\)="[^"]*"/g;
    
    const clickMatches = code.match(clickRegex);
    const keydownMatches = code.match(keydownRegex);
    
    return (clickMatches?.length || 0) > (keydownMatches?.length || 0);
  }

  private checkColorContrast(code: string): AnalysisResult[] {
    const results: AnalysisResult[] = [];
    
    // Basic check for hardcoded colors that might have contrast issues
    const colorRegex = /#[0-9a-fA-F]{3,6}/g;
    const colors = code.match(colorRegex);
    
    if (colors) {
      const lightColors = colors.filter(color => 
        this.isLightColor(color)
      );
      
      if (lightColors.length > 0) {
        results.push({
          type: 'info',
          message: 'Light colors detected - ensure sufficient contrast ratio',
          suggestion: 'Verify color contrast meets WCAG AA standards (4.5:1)',
        });
      }
    }
    
    return results;
  }

  private checkSemanticHTML(code: string): AnalysisResult[] {
    const results: AnalysisResult[] = [];
    
    // Check for div elements that should be buttons
    if (code.includes('<div') && code.includes('(click)')) {
      results.push({
        type: 'warning',
        message: 'Consider using semantic button elements instead of div with click handlers',
        suggestion: 'Replace clickable divs with <button> or <fin-button> elements',
      });
    }
    
    return results;
  }

  // Helper methods for best practices analysis

  private validateInputOutputNaming(sourceFile: ts.SourceFile): AnalysisResult[] {
    const results: AnalysisResult[] = [];
    
    ts.forEachChild(sourceFile, (node) => {
      if (ts.isClassDeclaration(node)) {
        node.members.forEach(member => {
          if (ts.isPropertyDeclaration(member) && member.modifiers) {
            const hasInputDecorator = member.modifiers.some((modifier: ts.ModifierLike) =>
              ts.isDecorator(modifier) &&
              ts.isCallExpression(modifier.expression) &&
              ts.isIdentifier(modifier.expression.expression) &&
              modifier.expression.expression.text === 'Input'
            );
            
            const hasOutputDecorator = member.modifiers.some((modifier: ts.ModifierLike) =>
              ts.isDecorator(modifier) &&
              ts.isCallExpression(modifier.expression) &&
              ts.isIdentifier(modifier.expression.expression) &&
              modifier.expression.expression.text === 'Output'
            );
            
            if (member.name && ts.isIdentifier(member.name)) {
              const name = member.name.text;
              
              if (hasOutputDecorator && !name.endsWith('Change') && !name.includes('Event')) {
                results.push({
                  type: 'info',
                  message: `Output '${name}' should follow naming convention`,
                  suggestion: 'Output names should describe events (e.g., valueChange, clicked)',
                });
              }
            }
          }
        });
      }
    });
    
    return results;
  }

  private findUnusedImports(sourceFile: ts.SourceFile): Array<{ name: string; range: Range }> {
    // This would require more sophisticated analysis
    // For now, return empty array
    return [];
  }

  private lacksErrorHandling(sourceFile: ts.SourceFile): boolean {
    const sourceText = sourceFile.getText();
    const hasHttpCalls = sourceText.includes('http.') || sourceText.includes('HttpClient');
    const hasErrorHandling = sourceText.includes('catchError') || 
                             sourceText.includes('.catch(') ||
                             sourceText.includes('try {');
    
    return hasHttpCalls && !hasErrorHandling;
  }

  private findMagicValues(sourceFile: ts.SourceFile): Array<{ type: string; value: string; range: Range }> {
    // This would require AST traversal to find numeric/string literals
    // For now, return empty array
    return [];
  }

  private analyzeFincloudUsage(code: string): AnalysisResult[] {
    const results: AnalysisResult[] = [];
    
    // Check for deprecated Fincloud components
    const deprecatedComponents = ['fin-old-button', 'fin-legacy-input'];
    
    for (const deprecated of deprecatedComponents) {
      if (code.includes(deprecated)) {
        results.push({
          type: 'warning',
          message: `Deprecated component '${deprecated}' detected`,
          suggestion: 'Migrate to the newer version of this component',
        });
      }
    }
    
    // Check for proper import usage
    if (code.includes('@fincloud/ui/') && !code.includes('src/index.ts')) {
      // This is actually correct - submodule imports are preferred
    }
    
    return results;
  }

  // Helper methods for performance report generation

  private calculateBundleSizeImpact(sourceFile: ts.SourceFile, metadata: any): BundleSizeImpact {
    const heavyDependencies = this.detectHeavyImports(sourceFile);
    
    return {
      estimatedSize: this.estimateComponentSize(sourceFile),
      heavyDependencies,
      optimizationSuggestions: this.generateBundleOptimizations(heavyDependencies),
    };
  }

  private analyzeRuntimeComplexity(sourceFile: ts.SourceFile): RuntimeComplexity {
    return {
      cyclomaticComplexity: this.calculateCyclomaticComplexity(sourceFile),
      nestedLoops: this.countNestedLoops(sourceFile),
      recursiveCallsDetected: this.hasRecursiveCalls(sourceFile),
    };
  }

  private analyzeMemoryUsage(sourceFile: ts.SourceFile): MemoryUsage {
    return {
      potentialMemoryLeaks: this.findMemoryLeaks(sourceFile),
      largeObjectAllocations: this.findLargeAllocations(sourceFile),
      unsubscribedObservables: this.findUnsubscribedObservables(sourceFile),
    };
  }

  private generatePerformanceRecommendations(
    changeDetection: AnalysisResult[],
    bundleSize: BundleSizeImpact,
    runtime: RuntimeComplexity,
    memory: MemoryUsage
  ): string[] {
    const recommendations: string[] = [];
    
    if (changeDetection.some(r => r.message.includes('OnPush'))) {
      recommendations.push('Implement OnPush change detection strategy');
    }
    
    if (bundleSize.heavyDependencies.length > 0) {
      recommendations.push('Consider tree-shaking or lighter alternatives for heavy dependencies');
    }
    
    if (runtime.cyclomaticComplexity > 10) {
      recommendations.push('Refactor complex methods to improve maintainability');
    }
    
    if (memory.unsubscribedObservables.length > 0) {
      recommendations.push('Implement proper observable cleanup');
    }
    
    return recommendations;
  }

  // Code fix generators

  private generateOnPushFix(sourceFile: ts.SourceFile): CodeFix {
    return {
      title: 'Add OnPush change detection',
      edits: [{
        range: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
        newText: 'import { ChangeDetectionStrategy } from \'@angular/core\';\n',
      }],
    };
  }

  private generateTrackByFix(): CodeFix {
    return {
      title: 'Add trackBy function',
      edits: [{
        range: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
        newText: 'trackByFn(index: number, item: any): any {\n  return item.id || index;\n}\n',
      }],
    };
  }

  private generateUnsubscribeFix(): CodeFix {
    return {
      title: 'Add unsubscribe logic',
      edits: [{
        range: { start: { line: 0, character: 0 }, end: { line: 0, character: 0 } },
        newText: 'private destroy$ = new Subject<void>();\n\nngOnDestroy(): void {\n  this.destroy$.next();\n  this.destroy$.complete();\n}\n',
      }],
    };
  }

  private generateRemoveImportFix(unused: { name: string; range: Range }): CodeFix {
    return {
      title: `Remove unused import '${unused.name}'`,
      edits: [{
        range: unused.range,
        newText: '',
      }],
    };
  }

  // Utility methods

  private isLightColor(hex: string): boolean {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return false;
    
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128;
  }

  private hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  }

  private estimateComponentSize(sourceFile: ts.SourceFile): number {
    // Rough estimation based on source file size and imports
    return sourceFile.getText().length / 1024; // KB
  }

  private generateBundleOptimizations(heavyDeps: string[]): string[] {
    return heavyDeps.map(dep => `Consider tree-shaking or alternatives for ${dep}`);
  }

  private calculateCyclomaticComplexity(sourceFile: ts.SourceFile): number {
    // Simplified complexity calculation
    const sourceText = sourceFile.getText();
    const complexityIndicators = [
      /if\s*\(/g,
      /else\s+if/g,
      /switch\s*\(/g,
      /case\s+/g,
      /for\s*\(/g,
      /while\s*\(/g,
      /catch\s*\(/g,
    ];
    
    let complexity = 1; // Base complexity
    
    for (const indicator of complexityIndicators) {
      const matches = sourceText.match(indicator);
      complexity += matches ? matches.length : 0;
    }
    
    return complexity;
  }

  private countNestedLoops(sourceFile: ts.SourceFile): number {
    // This would require proper AST analysis
    // For now, return 0
    return 0;
  }

  private hasRecursiveCalls(sourceFile: ts.SourceFile): boolean {
    // This would require analyzing function calls
    // For now, return false
    return false;
  }

  private findMemoryLeaks(sourceFile: ts.SourceFile): string[] {
    const leaks: string[] = [];
    const sourceText = sourceFile.getText();
    
    if (sourceText.includes('setInterval') && !sourceText.includes('clearInterval')) {
      leaks.push('Uncleaned interval detected');
    }
    
    if (sourceText.includes('setTimeout') && !sourceText.includes('clearTimeout')) {
      leaks.push('Potential uncleaned timeout detected');
    }
    
    return leaks;
  }

  private findLargeAllocations(sourceFile: ts.SourceFile): string[] {
    // This would require analyzing object allocations
    return [];
  }

  private findUnsubscribedObservables(sourceFile: ts.SourceFile): string[] {
    const unsubscribed: string[] = [];
    const sourceText = sourceFile.getText();
    
    if (sourceText.includes('.subscribe(') && 
        !sourceText.includes('unsubscribe') && 
        !sourceText.includes('takeUntil') &&
        !sourceText.includes('async')) {
      unsubscribed.push('Observable subscription without cleanup detected');
    }
    
    return unsubscribed;
  }
}