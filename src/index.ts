#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

import { FincloudUIIndexer } from "./indexers/component-indexer.js";
import { CompletionProvider } from "./completions/completion-provider.js";
import { DocumentationGenerator } from "./documentation/doc-generator.js";
import { CodeAnalyzer } from "./analysis/code-analyzer.js";
import { ConfigManager } from "./utils/config-manager.js";

class FincloudUIMCPServer {
  private server: McpServer;
  private indexer: FincloudUIIndexer;
  private completionProvider: CompletionProvider;
  private docGenerator: DocumentationGenerator;
  private codeAnalyzer: CodeAnalyzer;
  private configManager: ConfigManager;

  constructor() {
    this.server = new McpServer({
      name: "fincloud-ui-mcp-server",
      version: "1.0.0",
    });

    this.configManager = new ConfigManager();
    this.indexer = new FincloudUIIndexer(this.configManager);
    this.completionProvider = new CompletionProvider(this.indexer);
    this.docGenerator = new DocumentationGenerator(this.indexer);
    this.codeAnalyzer = new CodeAnalyzer(this.indexer);

    this.setupHandlers();
  }

  private setupHandlers() {
    // Register resources
    this.server.registerResource(
      "components",
      "fincloud://components",
      {
        title: "Fincloud UI Components",
        description: "List of all available UI components with metadata",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getComponentRegistry(), null, 2),
          },
        ],
      })
    );

    this.server.registerResource(
      "utils",
      "fincloud://utils",
      {
        title: "Fincloud Utils",
        description: "List of all utility functions and services",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getUtilsRegistry(), null, 2),
          },
        ],
      })
    );

    this.server.registerResource(
      "examples",
      "fincloud://examples",
      {
        title: "Code Examples",
        description: "Code examples for components and utilities",
        mimeType: "application/json",
      },
      async (uri) => ({
        contents: [
          {
            uri: uri.href,
            mimeType: "application/json",
            text: JSON.stringify(this.indexer.getExamples(), null, 2),
          },
        ],
      })
    );

    // Register tools
    this.server.registerTool(
      "get_component_completions",
      {
        title: "Component Completions",
        description:
          "Get code completion suggestions for Angular templates and TypeScript",
        inputSchema: {
          context: z
            .string()
            .describe("The code context where completion is requested"),
          position: z.object({
            line: z.number(),
            character: z.number(),
          }),
          filePath: z
            .string()
            .optional()
            .describe("Path to the file being edited"),
        },
      },
      async ({ context, position, filePath }) => {
        const completions = await this.completionProvider.getCompletions({
          context,
          position,
          filePath: filePath || "unknown",
        });

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(completions, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_component_documentation",
      {
        title: "Component Documentation",
        description: "Generate comprehensive documentation for a component",
        inputSchema: {
          componentName: z
            .string()
            .describe("Name of the component to document"),
          includeExamples: z
            .boolean()
            .optional()
            .default(true)
            .describe("Whether to include code examples"),
        },
      },
      async ({ componentName, includeExamples = true }) => {
        const documentation = await this.docGenerator.generateDocumentation(
          componentName,
          { includeExamples }
        );

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(documentation, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "analyze_code",
      {
        title: "Code Analysis",
        description: "Analyze code for best practices and potential issues",
        inputSchema: {
          filePath: z
            .string()
            .optional()
            .describe("Path to the file to analyze"),
          code: z
            .string()
            .optional()
            .describe(
              "Code content to analyze (optional if filePath provided)"
            ),
          analysisType: z
            .enum(["all", "performance", "accessibility", "best-practices"])
            .default("all")
            .describe("Type of analysis to perform"),
        },
      },
      async ({ filePath, code, analysisType = "all" }) => {
        const analysis = await this.codeAnalyzer.analyzeCode({
          filePath,
          code,
          analysisType,
        });

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(analysis, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "search_components",
      {
        title: "Search Components",
        description: "Search for components by name, functionality, or usage",
        inputSchema: {
          query: z.string().describe("Search query"),
          category: z
            .enum([
              "form",
              "layout",
              "navigation",
              "data-display",
              "feedback",
              "all",
            ])
            .default("all")
            .describe("Component category to search within"),
          includeUtils: z
            .boolean()
            .default(false)
            .describe("Whether to include utility functions in search"),
        },
      },
      async ({ query, category = "all", includeUtils = false }) => {
        const results = await this.indexer.searchComponents(query, {
          category,
          includeUtils,
        });

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(results, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_import_suggestions",
      {
        title: "Import Suggestions",
        description: "Get import path suggestions for components and utilities",
        inputSchema: {
          symbol: z.string().describe("Symbol name to find import for"),
          currentFilePath: z
            .string()
            .optional()
            .describe("Path of the current file"),
        },
      },
      async ({ symbol, currentFilePath }) => {
        const suggestions = await this.completionProvider.getImportSuggestions(
          symbol,
          currentFilePath
        );

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(suggestions, null, 2),
            },
          ],
        };
      }
    );

    this.server.registerTool(
      "get_related_components",
      {
        title: "Related Components",
        description: "Find components related to a given component",
        inputSchema: {
          componentName: z
            .string()
            .describe("Name of the component to find related components for"),
          relationType: z
            .enum([
              "dependencies",
              "dependents",
              "similar",
              "commonly-used-with",
            ])
            .default("commonly-used-with")
            .describe("Type of relationship to find"),
        },
      },
      async ({ componentName, relationType = "commonly-used-with" }) => {
        const related = await this.indexer.getRelatedComponents(
          componentName,
          relationType
        );

        return {
          content: [
            {
              type: "text",
              text: JSON.stringify(related, null, 2),
            },
          ],
        };
      }
    );
  }

  async run() {
    try {
      // Initialize the indexer
      console.error("Initializing Fincloud UI MCP Server...");
      console.error("Current working directory:", process.cwd());

      console.error("Starting indexer initialization...");
      await this.indexer.initialize();
      console.error("Indexer initialized successfully");

      console.error("Server initialization complete.");

      console.error("Creating transport...");
      const transport = new StdioServerTransport();
      console.error("Connecting to transport...");
      await this.server.connect(transport);
      console.error("Fincloud UI MCP Server running on stdio");
    } catch (error) {
      console.error("Error during server startup:");
      console.error("Error name:", (error as any)?.name || "Unknown");
      console.error("Error message:", (error as any)?.message || "No message");
      console.error("Error stack:", (error as any)?.stack || "No stack");
      throw error; // Re-throw to be caught by the outer catch
    }
  }
}

const server = new FincloudUIMCPServer();
server.run().catch((error) => {
  console.error("=== FATAL SERVER ERROR ===");
  console.error("Error type:", typeof error);
  console.error("Error name:", error?.name || "Unknown");
  console.error("Error message:", error?.message || "No message");
  console.error("Error code:", error?.code || "No code");
  console.error("Full error object:", JSON.stringify(error, null, 2));
  console.error("Error stack:");
  console.error(error?.stack || "No stack trace available");
  console.error("=== END ERROR DETAILS ===");
  process.exit(1);
});
