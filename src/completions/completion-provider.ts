import { readFile } from 'fs/promises';
import { resolve, join, dirname, relative } from 'path';
import { existsSync } from 'fs';

import { 
  CompletionItem, 
  CompletionItemKind, 
  ImportSuggestion, 
  Position, 
  ComponentSuggestion 
} from '../types/schema.js';
import { FincloudUIIndexer } from '../indexers/component-indexer.js';

export interface CompletionContext {
  context: string;
  position: Position;
  filePath: string;
}

export interface TemplateContext {
  type: 'element' | 'attribute' | 'binding' | 'directive' | 'text';
  element?: string;
  attribute?: string;
  bindingType?: 'property' | 'event' | 'two-way';
}

export class CompletionProvider {
  private indexer: FincloudUIIndexer;

  constructor(indexer: FincloudUIIndexer) {
    this.indexer = indexer;
  }

  async getCompletions(context: CompletionContext): Promise<CompletionItem[]> {
    const { context: code, position, filePath } = context;
    
    // Determine if we're in a TypeScript file or template
    if (filePath.endsWith('.component.html') || this.isInTemplate(code, position)) {
      return this.getTemplateCompletions(code, position, filePath);
    } else if (filePath.endsWith('.ts')) {
      return this.getTypeScriptCompletions(code, position, filePath);
    }
    
    return [];
  }

  private async getTemplateCompletions(
    template: string, 
    position: Position, 
    filePath: string
  ): Promise<CompletionItem[]> {
    const templateContext = this.analyzeTemplateContext(template, position);
    
    switch (templateContext.type) {
      case 'element':
        return this.getElementCompletions();
      case 'attribute':
        return this.getAttributeCompletions(templateContext.element || '');
      case 'binding':
        return this.getBindingCompletions(templateContext.element || '', templateContext.bindingType || 'property');
      case 'directive':
        return this.getDirectiveCompletions();
      default:
        return [];
    }
  }

  private async getTypeScriptCompletions(
    code: string, 
    position: Position, 
    filePath: string
  ): Promise<CompletionItem[]> {
    const line = this.getLineAtPosition(code, position);
    const currentWord = this.getCurrentWord(line, position.character);
    
    // Import statement completion
    if (this.isInImportStatement(line)) {
      return this.getImportPathCompletions(currentWord, filePath);
    }
    
    // Constructor injection completion
    if (this.isInConstructor(code, position)) {
      return this.getServiceCompletions();
    }
    
    // Method call completion on known component types
    if (this.isMethodCall(line)) {
      return this.getMethodCompletions(line, currentWord);
    }
    
    return [];
  }

  private getElementCompletions(): CompletionItem[] {
    const components = this.indexer.getComponentRegistry();
    const completions: CompletionItem[] = [];
    
    for (const [name, componentData] of Object.entries(components)) {
      const selector = componentData.api.selector;
      
      if (selector.startsWith('fin-')) {
        completions.push({
          label: selector,
          kind: CompletionItemKind.Class,
          detail: `${name} Component`,
          documentation: componentData.metadata.documentation,
          insertText: this.generateComponentSnippet(componentData.api),
          sortText: `0_${selector}`, // Prioritize fin- components
        });
      }
    }
    
    return completions;
  }

  private getAttributeCompletions(elementName: string): CompletionItem[] {
    const completions: CompletionItem[] = [];
    const component = this.findComponentBySelector(elementName);
    
    if (component) {
      // Add input properties
      for (const input of component.api.inputs) {
        completions.push({
          label: input.name,
          kind: CompletionItemKind.Property,
          detail: `Input: ${input.type}`,
          documentation: input.documentation,
          insertText: input.required ? `${input.name}="$1"` : `[${input.name}]="$1"`,
        });
      }
      
      // Add output events
      for (const output of component.api.outputs) {
        completions.push({
          label: `(${output.name})`,
          kind: CompletionItemKind.Event,
          detail: `Output: ${output.type}`,
          documentation: output.documentation,
          insertText: `(${output.name})="$1"`,
        });
      }
    }
    
    // Add common Angular directives
    const commonDirectives = [
      { name: '*ngIf', detail: 'Structural directive' },
      { name: '*ngFor', detail: 'Structural directive' },
      { name: '[class]', detail: 'Class binding' },
      { name: '[style]', detail: 'Style binding' },
      { name: '(click)', detail: 'Click event' },
    ];
    
    for (const directive of commonDirectives) {
      completions.push({
        label: directive.name,
        kind: CompletionItemKind.Keyword,
        detail: directive.detail,
        insertText: directive.name.includes('=') ? directive.name : `${directive.name}="$1"`,
      });
    }
    
    return completions;
  }

  private getBindingCompletions(
    elementName: string, 
    bindingType: 'property' | 'event' | 'two-way'
  ): CompletionItem[] {
    const component = this.findComponentBySelector(elementName);
    
    if (!component) {
      return [];
    }
    
    const completions: CompletionItem[] = [];
    
    if (bindingType === 'property') {
      for (const input of component.api.inputs) {
        completions.push({
          label: input.name,
          kind: CompletionItemKind.Property,
          detail: `Input: ${input.type}`,
          documentation: input.documentation,
          insertText: input.name,
        });
      }
    } else if (bindingType === 'event') {
      for (const output of component.api.outputs) {
        completions.push({
          label: output.name,
          kind: CompletionItemKind.Event,
          detail: `Output: ${output.type}`,
          documentation: output.documentation,
          insertText: output.name,
        });
      }
    }
    
    return completions;
  }

  private getDirectiveCompletions(): CompletionItem[] {
    const completions: CompletionItem[] = [];
    
    // Add common Angular directives
    const directives = [
      { name: 'ngIf', detail: 'Conditional rendering', insertText: '*ngIf="$1"' },
      { name: 'ngFor', detail: 'List rendering', insertText: '*ngFor="let item of $1"' },
      { name: 'ngSwitch', detail: 'Switch directive', insertText: '[ngSwitch]="$1"' },
    ];
    
    for (const directive of directives) {
      completions.push({
        label: directive.name,
        kind: CompletionItemKind.Keyword,
        detail: directive.detail,
        insertText: directive.insertText,
      });
    }
    
    return completions;
  }

  private getImportPathCompletions(currentWord: string, filePath: string): CompletionItem[] {
    const completions: CompletionItem[] = [];
    
    // Fincloud UI components
    if (currentWord.startsWith('@fincloud/ui')) {
      const components = this.indexer.getAllComponentNames();
      
      for (const componentName of components) {
        const metadata = this.indexer.getComponentMetadata(componentName);
        if (metadata) {
          completions.push({
            label: metadata.exportPath,
            kind: CompletionItemKind.Module,
            detail: `Import ${componentName}`,
            documentation: metadata.documentation,
            insertText: metadata.exportPath,
          });
        }
      }
    }
    
    // Fincloud Utils
    if (currentWord.startsWith('@fincloud/utils')) {
      const utilities = this.indexer.getAllUtilityNames();
      
      for (const utilityName of utilities) {
        // This would need more sophisticated logic to get the correct export path
        completions.push({
          label: `@fincloud/utils/${utilityName}`,
          kind: CompletionItemKind.Module,
          detail: `Import ${utilityName}`,
          insertText: `@fincloud/utils/${utilityName}`,
        });
      }
    }
    
    return completions;
  }

  private getServiceCompletions(): CompletionItem[] {
    const completions: CompletionItem[] = [];
    const utils = this.indexer.getUtilsRegistry();
    
    for (const [serviceName, serviceData] of Object.entries(utils.services)) {
      completions.push({
        label: serviceName,
        kind: CompletionItemKind.Class,
        detail: 'Injectable Service',
        documentation: serviceData.documentation,
        insertText: `private ${this.camelCase(serviceName)}: ${serviceName}`,
      });
    }
    
    return completions;
  }

  private getMethodCompletions(line: string, currentWord: string): CompletionItem[] {
    // This would require more sophisticated type analysis
    // For now, return empty array
    return [];
  }

  async getImportSuggestions(symbol: string, currentFilePath?: string): Promise<ImportSuggestion[]> {
    const suggestions: ImportSuggestion[] = [];
    
    // Search components
    const components = this.indexer.getComponentRegistry();
    for (const [name, componentData] of Object.entries(components)) {
      if (name.includes(symbol) || componentData.api.selector.includes(symbol)) {
        const importPath = this.resolveOptimalPath(
          componentData.metadata.exportPath, 
          currentFilePath || ''
        );
        
        suggestions.push({
          importPath,
          namedImports: [name],
          isDefault: false,
          confidence: this.calculateConfidence(name, symbol),
        });
      }
    }
    
    // Search utilities
    const utils = this.indexer.getUtilsRegistry();
    for (const [funcName, funcData] of Object.entries(utils.functions)) {
      if (funcName.includes(symbol)) {
        const importPath = this.resolveOptimalPath(
          funcData.exportPath,
          currentFilePath || ''
        );
        
        suggestions.push({
          importPath,
          namedImports: [funcName],
          isDefault: false,
          confidence: this.calculateConfidence(funcName, symbol),
        });
      }
    }
    
    return suggestions.sort((a, b) => b.confidence - a.confidence);
  }

  // Helper methods

  private isInTemplate(code: string, position: Position): boolean {
    // Check if cursor is within template literal or template file
    const line = this.getLineAtPosition(code, position);
    return line.includes('template:') || line.includes('`');
  }

  private analyzeTemplateContext(template: string, position: Position): TemplateContext {
    const lines = template.split('\n');
    const currentLine = lines[position.line] || '';
    const beforeCursor = currentLine.substring(0, position.character);
    const afterCursor = currentLine.substring(position.character);
    
    // Check if we're in an element tag
    if (beforeCursor.includes('<') && !beforeCursor.includes('>')) {
      if (beforeCursor.endsWith(' ') || beforeCursor.endsWith('\t')) {
        // We're in attribute position
        const elementMatch = beforeCursor.match(/<(\w[\w-]*)/);
        return {
          type: 'attribute',
          element: elementMatch ? elementMatch[1] : undefined,
        };
      } else {
        // We're typing element name
        return { type: 'element' };
      }
    }
    
    // Check if we're in a binding
    if (beforeCursor.includes('[') && !beforeCursor.includes(']')) {
      const elementMatch = currentLine.match(/<(\w[\w-]*)/);
      return {
        type: 'binding',
        element: elementMatch ? elementMatch[1] : undefined,
        bindingType: 'property',
      };
    }
    
    if (beforeCursor.includes('(') && !beforeCursor.includes(')')) {
      const elementMatch = currentLine.match(/<(\w[\w-]*)/);
      return {
        type: 'binding',
        element: elementMatch ? elementMatch[1] : undefined,
        bindingType: 'event',
      };
    }
    
    // Check for directive context
    if (beforeCursor.includes('*')) {
      return { type: 'directive' };
    }
    
    return { type: 'text' };
  }

  private getLineAtPosition(code: string, position: Position): string {
    const lines = code.split('\n');
    return lines[position.line] || '';
  }

  private getCurrentWord(line: string, character: number): string {
    const beforeCursor = line.substring(0, character);
    const match = beforeCursor.match(/[\w-@/]*$/);
    return match ? match[0] : '';
  }

  private isInImportStatement(line: string): boolean {
    return line.trim().startsWith('import') && line.includes('from');
  }

  private isInConstructor(code: string, position: Position): boolean {
    // Simple heuristic - check if we're in a constructor
    const lines = code.split('\n');
    for (let i = position.line; i >= 0; i--) {
      const line = lines[i];
      if (line.includes('constructor(')) {
        return true;
      }
      if (line.includes('}')) {
        break;
      }
    }
    return false;
  }

  private isMethodCall(line: string): boolean {
    return line.includes('.') && line.includes('(');
  }

  private findComponentBySelector(selector: string): any {
    const components = this.indexer.getComponentRegistry();
    
    for (const componentData of Object.values(components)) {
      if (componentData.api.selector === selector) {
        return componentData;
      }
    }
    
    return null;
  }

  private generateComponentSnippet(api: any): string {
    const inputs = api.inputs.filter((input: any) => input.required);
    
    if (inputs.length === 0) {
      return `<${api.selector}></${api.selector}>`;
    }
    
    const inputAttrs = inputs.map((input: any, index: number) => 
      `${input.name}="$${index + 1}"`
    ).join(' ');
    
    return `<${api.selector} ${inputAttrs}></${api.selector}>`;
  }

  private resolveOptimalPath(targetPath: string, fromPath: string): string {
    // Always prefer the @fincloud/* paths for this library
    if (targetPath.startsWith('@fincloud/')) {
      return targetPath;
    }
    
    // Fallback to relative path calculation if needed
    return targetPath;
  }

  private calculateConfidence(match: string, query: string): number {
    if (match === query) return 1.0;
    if (match.toLowerCase() === query.toLowerCase()) return 0.9;
    if (match.includes(query)) return 0.7;
    if (match.toLowerCase().includes(query.toLowerCase())) return 0.6;
    
    // Levenshtein distance based confidence
    const distance = this.levenshteinDistance(match.toLowerCase(), query.toLowerCase());
    const maxLength = Math.max(match.length, query.length);
    return Math.max(0, 1 - distance / maxLength);
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private camelCase(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1);
  }
}