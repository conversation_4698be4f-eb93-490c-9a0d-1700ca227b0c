import * as ts from "typescript";
import { readFile } from "fs/promises";
import { join, dirname } from "path";
import {
  ComponentMetadata,
  ComponentAPI,
  APIProperty,
  APIMethod,
  MethodParameter,
} from "../types/schema.js";

export interface ParsedComponent {
  metadata: ComponentMetadata;
  api: ComponentAPI;
  rawSource: string;
}

export interface ParsedService {
  className: string;
  methods: APIMethod[];
  properties: APIProperty[];
  injectionToken?: string;
  documentation: string;
}

export interface ParsedFunction {
  name: string;
  signature: string;
  parameters: MethodParameter[];
  returnType: string;
  documentation: string;
  typeParameters?: string[];
}

export class TypeScriptParser {
  private program: ts.Program;
  private checker: ts.TypeChecker;

  constructor(configPath: string) {
    // Validate TypeScript compiler API availability
    if (!ts.sys) {
      throw new Error(
        "TypeScript compiler system object (ts.sys) is not available. This may indicate an incompatible TypeScript version or ESM import issue."
      );
    }

    if (!ts.sys.readFile) {
      throw new Error(
        "TypeScript compiler readFile function (ts.sys.readFile) is not available."
      );
    }

    const configFile = ts.readConfigFile(configPath, ts.sys.readFile);

    if (configFile.error) {
      throw new Error(
        `Failed to read TypeScript config file: ${configFile.error.messageText}`
      );
    }

    const parsedCommandLine = ts.parseJsonConfigFileContent(
      configFile.config,
      ts.sys,
      dirname(configPath)
    );

    if (parsedCommandLine.errors && parsedCommandLine.errors.length > 0) {
      const errorMessages = parsedCommandLine.errors
        .map((err) => err.messageText)
        .join(", ");
      throw new Error(`TypeScript config parsing errors: ${errorMessages}`);
    }

    this.program = ts.createProgram({
      rootNames: parsedCommandLine.fileNames,
      options: parsedCommandLine.options,
    });

    this.checker = this.program.getTypeChecker();
  }

  async parseComponent(filePath: string): Promise<ParsedComponent | null> {
    try {
      const sourceFile = this.program.getSourceFile(filePath);
      if (!sourceFile) {
        throw new Error(`Source file not found: ${filePath}`);
      }

      const rawSource = await readFile(filePath, "utf-8");
      let componentClass: ts.ClassDeclaration | null = null;
      let componentDecorator: ts.Decorator | null = null;

      // Find the component class and decorator
      const visitor = (node: ts.Node): void => {
        if (ts.isClassDeclaration(node) && node.modifiers) {
          const compDecorator = node.modifiers.find(
            (modifier: ts.ModifierLike) =>
              ts.isDecorator(modifier) &&
              ts.isCallExpression(modifier.expression) &&
              ts.isIdentifier(modifier.expression.expression) &&
              modifier.expression.expression.text === "Component"
          ) as ts.Decorator | undefined;

          if (compDecorator) {
            componentClass = node;
            componentDecorator = compDecorator;
          }
        }
        ts.forEachChild(node, visitor);
      };

      sourceFile.forEachChild(visitor);

      if (!componentClass || !componentDecorator) {
        return null;
      }

      const metadata = this.extractComponentMetadata(
        componentClass,
        componentDecorator,
        filePath,
        rawSource
      );

      const api = this.extractComponentAPI(componentClass, componentDecorator);

      return { metadata, api, rawSource };
    } catch (error) {
      console.error(`Failed to parse component ${filePath}:`, error);
      return null;
    }
  }

  private extractComponentMetadata(
    classNode: ts.ClassDeclaration,
    decoratorNode: ts.Decorator,
    filePath: string,
    rawSource: string
  ): ComponentMetadata {
    const className = classNode.name?.text || "Unknown";
    const selector =
      this.extractDecoratorProperty(decoratorNode, "selector") || "";
    const documentation = this.extractJSDocComment(classNode) || "";

    // Extract module path from the file path
    const modulePath = this.getModulePath(filePath);
    const exportPath = this.getExportPath(filePath);

    // Extract dependencies from imports
    const dependencies = this.extractDependencies(rawSource);

    // Determine component category based on selector/class name
    const category = this.categorizeComponent(selector, className);

    // Extract tags from JSDoc or class name
    const tags = this.extractTags(classNode, selector);

    return {
      name: className,
      selector,
      modulePath,
      exportPath,
      filePath,
      dependencies,
      documentation,
      category,
      tags,
      deprecated: this.isDeprecated(classNode),
      since: this.extractSinceVersion(classNode),
    };
  }

  private extractComponentAPI(
    classNode: ts.ClassDeclaration,
    decoratorNode: ts.Decorator
  ): ComponentAPI {
    const selector =
      this.extractDecoratorProperty(decoratorNode, "selector") || "";
    const inputs = this.extractInputs(classNode);
    const outputs = this.extractOutputs(classNode);
    const methods = this.extractMethods(classNode);
    const contentProjection = this.extractContentProjection(decoratorNode);
    const hostBindings = this.extractHostBindings(classNode, decoratorNode);

    return {
      selector,
      inputs,
      outputs,
      methods,
      contentProjection,
      hostBindings,
    };
  }

  private extractInputs(classNode: ts.ClassDeclaration): APIProperty[] {
    const inputs: APIProperty[] = [];

    classNode.members.forEach((member) => {
      if (ts.isPropertyDeclaration(member) && member.modifiers) {
        const inputDecorator = member.modifiers.find(
          (modifier: ts.ModifierLike) =>
            ts.isDecorator(modifier) &&
            ts.isCallExpression(modifier.expression) &&
            ts.isIdentifier(modifier.expression.expression) &&
            modifier.expression.expression.text === "Input"
        ) as ts.Decorator | undefined;

        if (inputDecorator && member.name && ts.isIdentifier(member.name)) {
          const name = member.name.text;
          const type = this.getTypeString(member.type);
          const required = !member.questionToken;
          const documentation = this.extractJSDocComment(member) || "";
          const defaultValue = this.extractDefaultValue(member);

          inputs.push({
            name,
            type,
            required,
            defaultValue,
            documentation,
            examples: [],
            deprecated: this.isDeprecated(member),
          });
        }
      }
    });

    return inputs;
  }

  private extractOutputs(classNode: ts.ClassDeclaration): APIProperty[] {
    const outputs: APIProperty[] = [];

    classNode.members.forEach((member) => {
      if (ts.isPropertyDeclaration(member) && member.modifiers) {
        const outputDecorator = member.modifiers.find(
          (modifier: ts.ModifierLike) =>
            ts.isDecorator(modifier) &&
            ts.isCallExpression(modifier.expression) &&
            ts.isIdentifier(modifier.expression.expression) &&
            modifier.expression.expression.text === "Output"
        ) as ts.Decorator | undefined;

        if (outputDecorator && member.name && ts.isIdentifier(member.name)) {
          const name = member.name.text;
          const type = this.getTypeString(member.type);
          const documentation = this.extractJSDocComment(member) || "";

          outputs.push({
            name,
            type,
            required: false,
            documentation,
            examples: [],
            deprecated: this.isDeprecated(member),
          });
        }
      }
    });

    return outputs;
  }

  private extractMethods(classNode: ts.ClassDeclaration): APIMethod[] {
    const methods: APIMethod[] = [];

    classNode.members.forEach((member) => {
      if (
        ts.isMethodDeclaration(member) &&
        member.name &&
        ts.isIdentifier(member.name)
      ) {
        const isPublic =
          !member.modifiers?.some(
            (mod) =>
              mod.kind === ts.SyntaxKind.PrivateKeyword ||
              mod.kind === ts.SyntaxKind.ProtectedKeyword
          ) && !member.name.text.startsWith("_");

        if (isPublic) {
          const name = member.name.text;
          const parameters = this.extractMethodParameters(member);
          const returnType = this.getTypeString(member.type) || "void";
          const signature = this.buildMethodSignature(
            name,
            parameters,
            returnType
          );
          const documentation = this.extractJSDocComment(member) || "";

          methods.push({
            name,
            signature,
            parameters,
            returnType,
            documentation,
            isPublic: true,
          });
        }
      }
    });

    return methods;
  }

  private extractMethodParameters(
    method: ts.MethodDeclaration
  ): MethodParameter[] {
    return method.parameters.map((param) => {
      const name =
        param.name && ts.isIdentifier(param.name) ? param.name.text : "unknown";
      const type = this.getTypeString(param.type) || "any";
      const optional = !!param.questionToken;
      const documentation = this.extractJSDocComment(param);

      return { name, type, optional, documentation };
    });
  }

  async parseService(filePath: string): Promise<ParsedService | null> {
    try {
      const sourceFile = this.program.getSourceFile(filePath);
      if (!sourceFile) {
        return null;
      }

      let serviceClass: ts.ClassDeclaration | null = null;

      const visitor = (node: ts.Node): void => {
        if (ts.isClassDeclaration(node) && node.modifiers) {
          const injectable = node.modifiers.find(
            (modifier: ts.ModifierLike) =>
              ts.isDecorator(modifier) &&
              ts.isCallExpression(modifier.expression) &&
              ts.isIdentifier(modifier.expression.expression) &&
              modifier.expression.expression.text === "Injectable"
          ) as ts.Decorator | undefined;

          if (injectable) {
            serviceClass = node;
          }
        }
        ts.forEachChild(node, visitor);
      };

      sourceFile.forEachChild(visitor);

      if (!serviceClass) {
        return null;
      }

      const className =
        (serviceClass as ts.ClassDeclaration).name?.text || "Unknown";
      const methods = this.extractMethods(serviceClass);
      const properties = this.extractProperties(serviceClass);
      const documentation = this.extractJSDocComment(serviceClass) || "";

      return {
        className,
        methods,
        properties,
        documentation,
      };
    } catch (error) {
      console.error(`Failed to parse service ${filePath}:`, error);
      return null;
    }
  }

  async parseFunction(
    filePath: string,
    functionName: string
  ): Promise<ParsedFunction | null> {
    try {
      const sourceFile = this.program.getSourceFile(filePath);
      if (!sourceFile) {
        return null;
      }

      let functionDeclaration: ts.FunctionDeclaration | null = null;

      const visitor = (node: ts.Node): void => {
        if (
          ts.isFunctionDeclaration(node) &&
          node.name &&
          node.name.text === functionName
        ) {
          functionDeclaration = node;
        }
        ts.forEachChild(node, visitor);
      };

      sourceFile.forEachChild(visitor);

      if (!functionDeclaration) {
        return null;
      }

      const name =
        (functionDeclaration as ts.FunctionDeclaration).name?.text ||
        functionName;
      const parameters = this.extractFunctionParameters(functionDeclaration);
      const returnType =
        this.getTypeString(
          (functionDeclaration as ts.FunctionDeclaration).type
        ) || "any";
      const signature = this.buildMethodSignature(name, parameters, returnType);
      const documentation = this.extractJSDocComment(functionDeclaration) || "";
      const typeParameters = this.extractTypeParameters(functionDeclaration);

      return {
        name,
        signature,
        parameters,
        returnType,
        documentation,
        typeParameters,
      };
    } catch (error) {
      console.error(
        `Failed to parse function ${functionName} in ${filePath}:`,
        error
      );
      return null;
    }
  }

  private extractFunctionParameters(
    func: ts.FunctionDeclaration
  ): MethodParameter[] {
    return func.parameters.map((param) => {
      const name =
        param.name && ts.isIdentifier(param.name) ? param.name.text : "unknown";
      const type = this.getTypeString(param.type) || "any";
      const optional = !!param.questionToken;
      const documentation = this.extractJSDocComment(param);

      return { name, type, optional, documentation };
    });
  }

  private extractProperties(classNode: ts.ClassDeclaration): APIProperty[] {
    const properties: APIProperty[] = [];

    classNode.members.forEach((member) => {
      if (
        ts.isPropertyDeclaration(member) &&
        member.name &&
        ts.isIdentifier(member.name)
      ) {
        const isPublic =
          !member.modifiers?.some(
            (mod) =>
              mod.kind === ts.SyntaxKind.PrivateKeyword ||
              mod.kind === ts.SyntaxKind.ProtectedKeyword
          ) && !member.name.text.startsWith("_");

        if (isPublic) {
          const name = member.name.text;
          const type = this.getTypeString(member.type) || "any";
          const required = !member.questionToken;
          const documentation = this.extractJSDocComment(member) || "";
          const defaultValue = this.extractDefaultValue(member);

          properties.push({
            name,
            type,
            required,
            defaultValue,
            documentation,
            examples: [],
            deprecated: this.isDeprecated(member),
          });
        }
      }
    });

    return properties;
  }

  private getTypeString(typeNode: ts.TypeNode | undefined): string {
    if (!typeNode) return "any";
    return this.checker.typeToString(this.checker.getTypeAtLocation(typeNode));
  }

  private extractJSDocComment(node: ts.Node): string | undefined {
    const jsDocTags = ts.getJSDocTags(node);
    if (jsDocTags.length > 0) {
      return jsDocTags
        .map((tag) => tag.comment)
        .filter(Boolean)
        .join("\n");
    }
    return undefined;
  }

  private extractDecoratorProperty(
    decorator: ts.Decorator,
    propertyName: string
  ): string | undefined {
    if (
      ts.isCallExpression(decorator.expression) &&
      decorator.expression.arguments.length > 0
    ) {
      const arg = decorator.expression.arguments[0];
      if (ts.isObjectLiteralExpression(arg)) {
        const property = arg.properties.find(
          (prop) =>
            ts.isPropertyAssignment(prop) &&
            ts.isIdentifier(prop.name) &&
            prop.name.text === propertyName
        );

        if (
          property &&
          ts.isPropertyAssignment(property) &&
          ts.isStringLiteral(property.initializer)
        ) {
          return property.initializer.text;
        }
      }
    }
    return undefined;
  }

  private extractDependencies(source: string): string[] {
    const importMatches = source.match(/import\s+.*?\s+from\s+['"](.+?)['"]/g);
    if (!importMatches) return [];

    return importMatches
      .map((match) => {
        const pathMatch = match.match(/from\s+['"](.+?)['"]/);
        return pathMatch ? pathMatch[1] : null;
      })
      .filter((path): path is string => path !== null)
      .filter((path) => path.startsWith("@fincloud/"));
  }

  private getModulePath(filePath: string): string {
    const match = filePath.match(/libs\/ui\/([^/]+)/);
    return match ? `@fincloud/ui/${match[1]}` : "";
  }

  private getExportPath(filePath: string): string {
    const match = filePath.match(/libs\/(ui|utils)\/([^/]+)/);
    if (match) {
      return `@fincloud/${match[1]}/${match[2]}`;
    }
    return "";
  }

  private categorizeComponent(selector: string, className: string): string {
    const name = (selector + className).toLowerCase();

    if (name.includes("button") || name.includes("fab")) return "action";
    if (
      name.includes("input") ||
      name.includes("form") ||
      name.includes("field")
    )
      return "form";
    if (
      name.includes("modal") ||
      name.includes("dialog") ||
      name.includes("toast")
    )
      return "feedback";
    if (
      name.includes("nav") ||
      name.includes("menu") ||
      name.includes("breadcrumb")
    )
      return "navigation";
    if (
      name.includes("table") ||
      name.includes("chart") ||
      name.includes("list")
    )
      return "data-display";
    if (
      name.includes("container") ||
      name.includes("panel") ||
      name.includes("card")
    )
      return "layout";

    return "other";
  }

  private extractTags(node: ts.ClassDeclaration, selector: string): string[] {
    const tags: string[] = [];
    const jsDoc = this.extractJSDocComment(node);

    if (jsDoc) {
      const tagMatches = jsDoc.match(/@(\w+)/g);
      if (tagMatches) {
        tags.push(...tagMatches.map((tag) => tag.substring(1)));
      }
    }

    // Add selector-based tags
    if (selector.includes("fin-")) {
      tags.push("fincloud");
    }

    return [...new Set(tags)];
  }

  private isDeprecated(node: ts.Node): boolean {
    const jsDoc = this.extractJSDocComment(node);
    return jsDoc ? jsDoc.includes("@deprecated") : false;
  }

  private extractSinceVersion(node: ts.Node): string | undefined {
    const jsDoc = this.extractJSDocComment(node);
    if (jsDoc) {
      const match = jsDoc.match(/@since\s+([\d.]+)/);
      return match ? match[1] : undefined;
    }
    return undefined;
  }

  private extractDefaultValue(member: ts.PropertyDeclaration): any {
    if (member.initializer) {
      if (ts.isStringLiteral(member.initializer)) {
        return member.initializer.text;
      }
      if (ts.isNumericLiteral(member.initializer)) {
        return Number(member.initializer.text);
      }
      if (member.initializer.kind === ts.SyntaxKind.TrueKeyword) {
        return true;
      }
      if (member.initializer.kind === ts.SyntaxKind.FalseKeyword) {
        return false;
      }
    }
    return undefined;
  }

  private extractContentProjection(decorator: ts.Decorator): any[] {
    // This would require more complex parsing of the template
    // For now, return empty array
    return [];
  }

  private extractHostBindings(
    classNode: ts.ClassDeclaration,
    decorator: ts.Decorator
  ): any[] {
    // This would require parsing host bindings from the decorator
    // For now, return empty array
    return [];
  }

  private extractTypeParameters(
    func: ts.FunctionDeclaration
  ): string[] | undefined {
    if (func.typeParameters) {
      return func.typeParameters.map((tp) => tp.name.text);
    }
    return undefined;
  }

  private buildMethodSignature(
    name: string,
    parameters: MethodParameter[],
    returnType: string
  ): string {
    const params = parameters
      .map((p) => `${p.name}${p.optional ? "?" : ""}: ${p.type}`)
      .join(", ");
    return `${name}(${params}): ${returnType}`;
  }
}
