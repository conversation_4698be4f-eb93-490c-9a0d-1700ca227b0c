#!/usr/bin/env node

/**
 * Setup script for Fincloud UI MCP Server integration
 * Helps configure the server for different project setups
 */

import { writeFile, readFile, access } from 'fs/promises';
import { join, resolve, relative } from 'path';
import { existsSync } from 'fs';

const SETUP_TEMPLATES = {
  local: {
    name: 'Local Development',
    description: 'Server and library on the same machine',
    mcpConfig: (serverPath, libraryPath) => ({
      mcpServers: {
        'fincloud-ui': {
          command: 'node',
          args: [join(relative(process.cwd(), serverPath), 'dist/index.js')],
          cwd: relative(process.cwd(), serverPath),
          env: {
            FINCLOUD_LIBRARY_PATH: relative(serverPath, libraryPath)
          }
        }
      }
    })
  },
  
  global: {
    name: 'Global Installation',
    description: 'Server installed globally via npm',
    mcpConfig: (serverPath, libraryPath) => ({
      mcpServers: {
        'fincloud-ui': {
          command: 'fincloud-ui-mcp',
          env: {
            FINCLOUD_LIBRARY_PATH: resolve(libraryPath)
          }
        }
      }
    })
  },
  
  absolute: {
    name: 'Absolute Paths',
    description: 'Using absolute paths (most reliable)',
    mcpConfig: (serverPath, libraryPath) => ({
      mcpServers: {
        'fincloud-ui': {
          command: 'node',
          args: [join(resolve(serverPath), 'dist/index.js')],
          cwd: resolve(serverPath),
          env: {
            FINCLOUD_LIBRARY_PATH: resolve(libraryPath)
          }
        }
      }
    })
  }
};

async function findFincloudLibrary(startPath = process.cwd()) {
  const commonNames = ['lib-ui-2', 'fincloud-ui', 'ui-library', 'libs'];
  const searchPaths = [
    startPath,
    join(startPath, '..'),
    join(startPath, '../..'),
    join(startPath, '../../..')
  ];

  for (const searchPath of searchPaths) {
    for (const name of commonNames) {
      const candidatePath = join(searchPath, name);
      const uiPath = join(candidatePath, 'libs', 'ui');
      
      if (existsSync(uiPath)) {
        return candidatePath;
      }
    }
  }
  
  return null;
}

async function findMCPServer(startPath = process.cwd()) {
  const commonNames = ['fincloud-ui-mcp-server', 'mcp-server'];
  const searchPaths = [
    startPath,
    join(startPath, '..'),
    join(startPath, '../..'),
    join(startPath, '../../..')
  ];

  for (const searchPath of searchPaths) {
    for (const name of commonNames) {
      const candidatePath = join(searchPath, name);
      const indexPath = join(candidatePath, 'dist', 'index.js');
      
      if (existsSync(indexPath)) {
        return candidatePath;
      }
    }
  }
  
  return null;
}

async function promptUser(question, defaultValue = '') {
  // In a real implementation, you'd use readline or inquirer
  // For now, return default or throw error
  if (defaultValue) {
    console.log(`${question} (using default: ${defaultValue})`);
    return defaultValue;
  }
  throw new Error(`Please provide: ${question}`);
}

async function setupMCP() {
  console.log('🚀 Fincloud UI MCP Server Setup\n');

  try {
    // Auto-detect paths
    console.log('🔍 Auto-detecting paths...');
    const serverPath = await findMCPServer();
    const libraryPath = await findFincloudLibrary();

    console.log(`Server path: ${serverPath || 'Not found'}`);
    console.log(`Library path: ${libraryPath || 'Not found'}\n`);

    // Choose setup type
    console.log('📋 Available setup types:');
    Object.entries(SETUP_TEMPLATES).forEach(([key, template]) => {
      console.log(`  ${key}: ${template.name} - ${template.description}`);
    });

    const setupType = process.argv[2] || 'local';
    const template = SETUP_TEMPLATES[setupType];
    
    if (!template) {
      throw new Error(`Unknown setup type: ${setupType}`);
    }

    console.log(`\n✅ Using setup type: ${template.name}`);

    // Validate paths
    const finalServerPath = serverPath || await promptUser('Enter MCP server path:');
    const finalLibraryPath = libraryPath || await promptUser('Enter Fincloud UI library path:');

    if (!existsSync(join(finalServerPath, 'dist', 'index.js'))) {
      throw new Error(`MCP server not found at: ${finalServerPath}/dist/index.js`);
    }

    if (!existsSync(join(finalLibraryPath, 'libs', 'ui'))) {
      throw new Error(`Fincloud UI library not found at: ${finalLibraryPath}/libs/ui`);
    }

    // Generate configurations
    const mcpConfig = template.mcpConfig(finalServerPath, finalLibraryPath);
    
    // Write .mcp.json
    const mcpJsonPath = join(process.cwd(), '.mcp.json');
    await writeFile(mcpJsonPath, JSON.stringify(mcpConfig, null, 2));
    console.log(`✅ Created ${mcpJsonPath}`);

    // Create server config if needed
    const serverConfigPath = join(finalServerPath, 'fincloud-ui-mcp.config.json');
    if (!existsSync(serverConfigPath)) {
      const serverConfig = {
        libraryPath: relative(finalServerPath, finalLibraryPath),
        cacheDir: '.mcp-cache',
        watchFiles: true,
        analysisRules: {
          enforceOnPush: true,
          checkAccessibility: true,
          validateImports: true,
          performanceChecks: true
        },
        completionSettings: {
          includePrivateMembers: false,
          fuzzyMatching: true,
          maxSuggestions: 50,
          prioritizeRecentlyUsed: true
        }
      };
      
      await writeFile(serverConfigPath, JSON.stringify(serverConfig, null, 2));
      console.log(`✅ Created ${serverConfigPath}`);
    }

    console.log('\n🎉 Setup complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Restart your IDE/editor');
    console.log('2. The Fincloud UI MCP server should now be available');
    console.log('3. Test by asking about Fincloud components in your AI assistant');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.log('\n📖 Manual setup instructions:');
    console.log('1. Create .mcp.json in your project root');
    console.log('2. Configure the server path and library path');
    console.log('3. See README.md for detailed examples');
    process.exit(1);
  }
}

// CLI usage
if (process.argv[1].endsWith('setup-mcp.js')) {
  setupMCP();
}

export { setupMCP, SETUP_TEMPLATES };
